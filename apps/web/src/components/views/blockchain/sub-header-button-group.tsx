'use client';

import { useMemo } from 'react';
import { BiBookOpen } from 'react-icons/bi';
import { Button, Link, Text } from '@repo/ui/components';
import { appRoutes, useWindowSize } from '@repo/ui/lib';
import { useScrollHeight } from '@/lib/hooks/use-scroll-height';

export default function SubHeaderButtonGroup() {
  const defaultScrollY = 70;
  const { scrollY } = useScrollHeight();
  const { isMobile } = useWindowSize();

  const calcPaddingY = useMemo(() => {
    if (isMobile) {
      return '32px';
    }

    if (scrollY > defaultScrollY) {
      return '15px';
    }

    return `${20 - (5 * scrollY) / defaultScrollY}px`;
  }, [scrollY, isMobile]);

  const calcTransform = useMemo(() => {
    if (isMobile) {
      return 'translate(0px, -140px)';
    }

    if (scrollY > defaultScrollY) {
      return 'translate(0px, -50px)';
    }

    return `translate(0px, ${(-50 * scrollY) / defaultScrollY}px)`;
  }, [scrollY, isMobile]);

  const calcPaddingLeft = useMemo(() => {
    if (isMobile) {
      return '30px';
    }

    if (scrollY > defaultScrollY) {
      return '200px';
    }

    return `${80 + (120 * scrollY) / defaultScrollY}px`;
  }, [scrollY, isMobile]);

  return (
    <div
      className='absolute z-[1] flex w-full flex-row gap-3 border-b border-[#303030] bg-[#121212]'
      style={{
        paddingTop: isMobile ? '100px' : calcPaddingY,
        paddingBottom: calcPaddingY,
        transform: calcTransform,
        paddingLeft: calcPaddingLeft,
      }}
    >
      <Button
        size='sm'
        className='flex flex-row items-center gap-1.5 bg-[#D9D9D9] px-6 py-1.5 sm:px-9'
      >
        <Text level='sm' className='font-medium text-black'>
          Primary CTA
        </Text>
      </Button>
      <Button
        asChild
        size='sm'
        variant='secondary'
        className='flex flex-row items-center gap-2.5 border border-solid border-[#393939] bg-[#1E1E1E] px-5 py-1.5 sm:px-8'
      >
        <Link href={appRoutes.subnets.doc} target='_blank'>
          <BiBookOpen size={15} />
          <Text level='sm' className='font-medium'>
            View Docs
          </Text>
        </Link>
      </Button>
    </div>
  );
}
