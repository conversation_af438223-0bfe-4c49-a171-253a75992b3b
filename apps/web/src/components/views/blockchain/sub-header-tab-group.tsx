'use client';

import { useEffect, useMemo, useState } from 'react';
import { usePathname } from 'next/navigation';
import { Link } from '@repo/ui/components';
import { cn, useWindowSize } from '@repo/ui/lib';
import {
  blockchainGroupTitles,
  navigationLinks,
} from '@/lib/constants/data/navigation-links';
import { useScrollHeight } from '@/lib/hooks/use-scroll-height';

export default function SubHeaderTabGroup({ variant }: { variant: string }) {
  const defaultScrollY = 70;

  const pathname = usePathname();
  const { scrollY } = useScrollHeight();
  const { isMobile } = useWindowSize();

  const [currentTab, setCurrentTab] = useState<string>();

  const subLinks = useMemo(
    () =>
      navigationLinks
        .find((item) => item.label === 'Blockchain')
        ?.children.filter((item) =>
          variant === 'Dtao'
            ? item.groupTitle === blockchainGroupTitles.dtao
            : variant === 'EVM'
              ? item.groupTitle === blockchainGroupTitles.evm
              : item.groupTitle !== blockchainGroupTitles.evm &&
                item.groupTitle !== blockchainGroupTitles.dtao &&
                item.label !== 'Tokenomics'
        ) ?? [],
    [variant]
  );

  const calcPaddingY = useMemo(() => {
    if (isMobile) {
      return '32px';
    }

    if (scrollY > defaultScrollY) {
      return '50px';
    }

    return `${(50 * scrollY) / defaultScrollY}px`;
  }, [scrollY, isMobile]);

  const calcTransform = useMemo(() => {
    if (isMobile) {
      return 'translate(0px, -116px)';
    }

    if (scrollY > defaultScrollY) {
      return 'translate(0px, -92px)';
    }

    return `translate(0px, ${(-92 * scrollY) / defaultScrollY}px)`;
  }, [scrollY, isMobile]);

  const calcPaddingLeft = useMemo(() => {
    if (isMobile) {
      return '10px';
    }

    if (scrollY > defaultScrollY) {
      return '200px';
    }

    return `${80 + (120 * scrollY) / defaultScrollY}px`;
  }, [scrollY, isMobile]);

  useEffect(() => {
    if (pathname.includes('blockchain')) {
      setCurrentTab('Accounts');
      return;
    }

    if (pathname.includes('txs')) {
      setCurrentTab('Transactions');
      return;
    }

    if (pathname.includes('delegation')) {
      setCurrentTab('Staking Transactions');
      return;
    }

    for (const link of subLinks) {
      if (
        pathname.split('/').pop() ===
        link.label.split(' ').join('-').toLowerCase()
      ) {
        setCurrentTab(link.label);
        return;
      }
    }
  }, [pathname, subLinks]);

  return (
    <div
      className='absolute z-[1] flex w-full scale-50 flex-row overflow-x-auto border-b border-[#303030] bg-[#121212] max-md:justify-between lg:gap-3'
      style={{
        paddingTop: isMobile ? '90px' : calcPaddingY,
        transform: calcTransform,
        paddingLeft: calcPaddingLeft,
        paddingRight: calcPaddingLeft,
      }}
    >
      {subLinks.map(({ href, label }) => (
        <Link
          href={href}
          key={label}
          className={cn(
            'whitespace-nowrap border-[#00d9ba] px-3 py-4 text-xs transition-colors sm:px-4 sm:py-5 md:text-sm lg:px-5 lg:py-3',
            currentTab === label
              ? 'border-b text-[#00d9ba]'
              : 'text-[#FFFFFF80] hover:text-[#00d9ba]'
          )}
        >
          {label}
        </Link>
      ))}
    </div>
  );
}
