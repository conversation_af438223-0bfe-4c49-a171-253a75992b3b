import { Link, Text } from '@repo/ui/components';

export default function AboutSection({
  variant,
}: {
  variant?: 'Blockchain' | 'Subnets' | 'EVM' | 'Dtao';
}) {
  if (variant === 'Blockchain') {
    return (
      <div className='absolute right-10 top-7 z-[2] hidden flex-col gap-1.5 lg:flex'>
        <Text level='base' className='font-normal'>
          About
        </Text>
        <Text
          level='sm'
          className='max-w-120.5 line-clamp-3 font-normal text-[#626262]'
        >
          Bittensor is a Decentralised Incentivised Machine Learning Network and
          Digital Commodities Market. Decentralised: Unlike a centralised
          company where the infrastructure is controlled by a single entity,
          Bittensor is run on a distributed network of computers that are owned
          and operated by many (thousands) of different individuals or
          companies. This decentralisation improves resilience, and removes
          central points of failure. Incentivised: Incentivisation is achieved
          through the use of Bittensor&#39;s native token $TAO. Participants are
          rewarded with tokens proportional to the value of their contribution
          to the network, with the lowest valued participants being replaced at
          a defined period - ensuring that the entire network is not only
          performant but also strives to improve. Machine Learning Network
          Bittensor was designed around machine learning tasks. One of the
          biggest concerns in machine learning is compute power, and a
          decentralised machine learning network provides Bittensor participants
          with access to immense computing power. Digital Commodities Market
          Although designed for machine learning, the (Re)evolution of Bittensor
          and the data agnostic principles of Yuma consensus have allowed it to
          adapt itself to not only provide a marketplace for Intelligence but
          for any digital commodity that can be produced and valued by network
          participants.
        </Text>
      </div>
    );
  }

  return (
    <div className='absolute right-10 top-6 z-[2] hidden flex-col gap-1.5 lg:flex'>
      <Text level='base' className='font-normal'>
        About
      </Text>
      <Text
        level='sm'
        className='max-w-120.5 line-clamp-3 font-normal text-[#626262]'
      >
        Bittensor currently has 32 independently run sub-networks (subnets),
        with each subnet providing a{' '}
        <Link
          className='decoration-ocean hover:text-ocean underline transition-colors'
          href='https://docs.taostats.io/docs/incentive-mechanisms'
        >
          unique set of rules
        </Link>{' '}
        by which the participants produce the intelligence or digital commodity
        for which the subnet provides incentive. These tasks are performed by{' '}
        <Link
          className='decoration-ocean hover:text-ocean underline transition-colors'
          href='https://docs.taostats.io/docs/mining'
        >
          Miners
        </Link>{' '}
        with the incentive value defined by a rewards landscape created by the
        coordinated contribution of Validators, who are responsible for
        verifying and scoring the outputs of the miners.
      </Text>
    </div>
  );
}
