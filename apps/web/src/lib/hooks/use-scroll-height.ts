'use client';

import { useCallback, useEffect, useState } from 'react';

export const useScrollHeight = () => {
  const [scrollY, setScrollY] = useState<number>(0);

  const handleScroll = useCallback(() => {
    const { scrollY: windowsScrollY } = window;

    setScrollY(windowsScrollY < 0 ? 0 : windowsScrollY);
  }, []);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);

    return () => {
      // Cleaning up the listener on unmounting
      window.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  return {
    scrollY,
  };
};
