import {
  AreaChart,
  Box,
  CircleDollarSign,
  type LucideIcon,
  Network,
} from 'lucide-react';
import type { Route } from 'next';
import { appRoutes } from '@repo/ui/lib';

export enum blockchainGroupTitles {
  substrate = 'Substrate',
  evm = 'EVM',
  core = 'Core',
  dtao = 'Dtao',
}

export enum analyticsTitles {
  price = 'Price',
  transactions = 'Transactions',
  subnets = 'Subnets & Chain',
}

export type GroupedNavigationItem = {
  label: string;
  href: Route;
  groupTitle?: string;
};

type NavigationLink = {
  label: string;
  Icon: LucideIcon;
  href: Route;
  children: GroupedNavigationItem[];
};

export const navigationLinks: NavigationLink[] = [
  {
    label: 'Blockchain',
    Icon: Box,
    href: appRoutes.blockchain.blocks,
    children: [
      {
        label: 'Blocks',
        href: appRoutes.blockchain.blocks,
        groupTitle: blockchainGroupTitles.substrate,
      },
      {
        label: 'Transfers',
        href: appRoutes.blockchain.transfer,
        groupTitle: blockchainGroupTitles.substrate,
      },
      {
        label: 'Staking Transactions',
        href: appRoutes.blockchain.delegation,
        groupTitle: blockchainGroupTitles.substrate,
      },
      {
        label: 'Accounts',
        href: appRoutes.blockchain.accounts,
        groupTitle: blockchainGroupTitles.substrate,
      },
      {
        label: 'Extrinsics',
        href: appRoutes.blockchain.extrinsic,
        groupTitle: blockchainGroupTitles.substrate,
      },
      {
        label: 'Events',
        href: appRoutes.blockchain.event,
        groupTitle: blockchainGroupTitles.substrate,
      },
      {
        label: 'Blocks',
        href: appRoutes.evm.blocks,
        groupTitle: blockchainGroupTitles.evm,
      },
      {
        label: 'Transactions',
        href: appRoutes.evm.transaction,
        groupTitle: blockchainGroupTitles.evm,
      },
      {
        label: 'Contracts',
        href: appRoutes.evm.contracts,
        groupTitle: blockchainGroupTitles.evm,
      },
      {
        label: 'EVM Explorer (beta)',
        href: 'https://evm.taostats.io',
        groupTitle: blockchainGroupTitles.evm,
      },
      {
        label: 'Runtime',
        href: appRoutes.blockchain.runtime,
        groupTitle: blockchainGroupTitles.core,
      },
      {
        label: 'Sudo',
        href: appRoutes.blockchain.sudo,
        groupTitle: blockchainGroupTitles.core,
      },
      // ...(API_ENDPOINT_V2.includes("prod")
      // 	? []
      // 	: [
      // 		{
      // 			label: "Pool",
      // 			href: appRoutes.dtao.pool,
      // 			groupTitle: blockchainGroupTitles.dtao,
      // 		},
      // 		{
      // 			label: "Subnet Emission",
      // 			href: appRoutes.dtao.subnetEmission,
      // 			groupTitle: blockchainGroupTitles.dtao,
      // 		},
      // 		{
      // 			label: "Hotkey Emission",
      // 			href: appRoutes.dtao.hotkeyEmission,
      // 			groupTitle: blockchainGroupTitles.dtao,
      // 		},
      // 		{
      // 			label: "Hotkey Alpha Shares",
      // 			href: appRoutes.dtao.hotkeyAlphaShares,
      // 			groupTitle: blockchainGroupTitles.dtao,
      // 		},
      // 		{
      // 			label: "Coldkey Alpha Shares",
      // 			href: appRoutes.dtao.coldkeyAlphaShares,
      // 			groupTitle: blockchainGroupTitles.dtao,
      // 		},
      // 		{
      // 			label: "Stake Balance",
      // 			href: appRoutes.dtao.stakeBalance,
      // 			groupTitle: blockchainGroupTitles.dtao,
      // 		},
      // 		{
      // 			label: "Stake Balance Aggregated",
      // 			href: appRoutes.dtao.stakeBalanceAggregated,
      // 			groupTitle: blockchainGroupTitles.dtao,
      // 		},
      // 	]),
    ],
  },
  {
    label: 'Validators',
    href: appRoutes.validator.home,
    Icon: Network,
    children: [{ label: 'Yield', href: appRoutes.yield }],
  },
  {
    label: 'Analytics',
    Icon: AreaChart,
    href: appRoutes.analytics.home,
    children: [
      {
        label: 'Staking vs Price History',
        href: appRoutes.analytics.chartDetails('stats'),
        groupTitle: analyticsTitles.price,
      },
      {
        label: 'Daily Price Changes',
        href: appRoutes.analytics.chartDetails('price'),
        groupTitle: analyticsTitles.price,
      },
      // {
      // 	label: "Tao Price Percentage Change",
      // 	href: appRoutes.analytics.chartDetails("percentage"),
      // 	groupTitle: analyticsTitles.price,
      // },
      {
        label: 'Recent Exchange Transactions',
        href: appRoutes.analytics.chartDetails('exchanges'),
        groupTitle: analyticsTitles.transactions,
      },
      {
        label: 'New Account Balances',
        href: appRoutes.analytics.chartDetails('accounts'),
        groupTitle: analyticsTitles.transactions,
      },
      {
        label: 'Heatmap of Hotkey vs Netuid',
        href: appRoutes.analytics.chartDetails('heatmap'),
        groupTitle: analyticsTitles.transactions,
      },
      {
        label: 'Top 50 Bittensor Account Balances',
        href: appRoutes.analytics.chartDetails('balances'),
        groupTitle: analyticsTitles.transactions,
      },
      {
        label: 'Recent Largest Transactions',
        href: appRoutes.analytics.chartDetails('transactions'),
        groupTitle: analyticsTitles.transactions,
      },
      {
        label: 'Realtime Transaction Volume',
        href: appRoutes.analytics.chartDetails('latest'),
        groupTitle: analyticsTitles.transactions,
      },
      {
        label: 'Chain Block Production',
        href: appRoutes.analytics.chartDetails('blocks'),
        groupTitle: analyticsTitles.subnets,
      },
      {
        label: 'Miner Age vs. Incentive',
        href: appRoutes.analytics.chartDetails('incentive'),
        groupTitle: analyticsTitles.subnets,
      },
      {
        label: '24h Subnet Emissions vs. Recycled',
        href: appRoutes.analytics.chartDetails('emissions'),
        groupTitle: analyticsTitles.subnets,
      },
      {
        label: 'Subnet Growth',
        href: appRoutes.analytics.chartDetails('subnets'),
        groupTitle: analyticsTitles.subnets,
      },
      {
        label: 'Tao Staked Over Time',
        href: appRoutes.analytics.chartDetails('tao-stake-over-time'),
        groupTitle: analyticsTitles.subnets,
      },
      {
        label: 'Extrinsics Outcomes',
        href: appRoutes.analytics.chartDetails('extrinsic'),
        groupTitle: analyticsTitles.subnets,
      },
      {
        label: 'Daily Registration Recycle',
        href: appRoutes.analytics.chartDetails('recycle'),
        groupTitle: analyticsTitles.subnets,
      },
      {
        label: 'Historical Root',
        href: appRoutes.analytics.historical,
        groupTitle: analyticsTitles.subnets,
      },
    ],
  },
];

export const InvestorLinks = {
  label: 'Investors',
  Icon: CircleDollarSign,
  href: appRoutes.blockchain.tokenomics,
  children: [
    {
      label: 'Yield',
      href: appRoutes.yield,
    },
    {
      label: 'Tokenomics',
      href: appRoutes.blockchain.tokenomics,
    },
    {
      label: 'Where to Buy',
      href: appRoutes.buy,
    },
  ],
};
